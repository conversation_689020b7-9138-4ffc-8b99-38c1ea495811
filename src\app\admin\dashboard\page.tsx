'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface AdminStats {
  totalArtworks: number;
  availableArtworks: number;
  soldArtworks: number;
  totalLikes: number;
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<AdminStats>({
    totalArtworks: 0,
    availableArtworks: 0,
    soldArtworks: 0,
    totalLikes: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return; // Still loading
    if (!session) {
      router.push('/admin/login');
      return;
    }
    fetchStats();
  }, [session, status, router]);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-serif font-bold text-gray-900">
            Admin Dashboard
          </h1>
          <p className="mt-2 text-gray-600">
            Welcome back! Manage your artwork portfolio.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Artworks</h3>
            <p className="text-2xl font-bold text-gray-900">
              {loading ? '...' : stats.totalArtworks}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Available</h3>
            <p className="text-2xl font-bold text-green-600">
              {loading ? '...' : stats.availableArtworks}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Sold</h3>
            <p className="text-2xl font-bold text-red-600">
              {loading ? '...' : stats.soldArtworks}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Likes</h3>
            <p className="text-2xl font-bold text-blue-600">
              {loading ? '...' : stats.totalLikes}
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <a href="/admin/add-artwork" className="block p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                <div className="text-center">
                  <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <p className="mt-2 text-sm font-medium text-gray-900">Add New Artwork</p>
                  <p className="text-xs text-gray-500">Upload and manage your paintings</p>
                </div>
              </a>
              
              <a href="/admin/manage-gallery" className="block p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                <div className="text-center">
                  <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <p className="mt-2 text-sm font-medium text-gray-900">Manage Gallery</p>
                  <p className="text-xs text-gray-500">Edit existing artworks</p>
                </div>
              </a>
              
              <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                <div className="text-center">
                  <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <p className="mt-2 text-sm font-medium text-gray-900">View Analytics</p>
                  <p className="text-xs text-gray-500">See likes and engagement</p>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Database Health */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Database Health (Neon Tier Status)</h2>
              <button
                onClick={async () => {
                  try {
                    const response = await fetch('/api/admin/maintenance', { method: 'POST' });
                    const data = await response.json();
                    if (data.success) {
                      alert('Database maintenance completed successfully!');
                      fetchStats(); // Refresh stats
                    }
                  } catch (error) {
                    alert('Maintenance failed');
                  }
                }}
                className="text-sm bg-blue-50 text-blue-700 px-3 py-1 rounded-md hover:bg-blue-100 transition-colors"
              >
                Run Maintenance
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {loading ? '...' : `${stats.totalArtworks + stats.totalLikes}`}
                </p>
                <p className="text-sm text-gray-500">Total Records</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {loading ? '...' : `~${Math.round((stats.totalArtworks * 1 + stats.totalLikes * 0.064) * 100) / 100}`}KB
                </p>
                <p className="text-sm text-gray-500">Estimated Size</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-600">
                  {loading ? '...' : `${Math.round(((stats.totalArtworks * 1024 + stats.totalLikes * 64) / (0.5 * 1024 * 1024 * 1024)) * 10000) / 100}%`}
                </p>
                <p className="text-sm text-gray-500">of 0.5GB Limit</p>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Neon Tier Status</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Storage Usage:</span>
                  <span className="text-green-600">Excellent ✓</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Record Count:</span>
                  <span className={stats.totalArtworks > 100 ? "text-yellow-600" : "text-green-600"}>
                    {stats.totalArtworks > 100 ? "Monitor" : "Good"} ✓
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Optimization:</span>
                  <span className="text-blue-600">Auto-maintained</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
          </div>
          <div className="p-6">
            {stats.totalArtworks > 0 ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Total artworks uploaded</span>
                  <span className="text-sm font-medium text-gray-900">{stats.totalArtworks}</span>
                </div>
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Total likes received</span>
                  <span className="text-sm font-medium text-gray-900">{stats.totalLikes}</span>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">Available for sale</span>
                  <span className="text-sm font-medium text-green-600">{stats.availableArtworks}</span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">
                No recent activity. Start by adding your first artwork!
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}