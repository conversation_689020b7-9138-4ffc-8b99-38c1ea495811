import { Metadata } from 'next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
}

export function generateMetadata({
  title = 'Artist Portfolio - Original Paintings & Artwork',
  description = 'Discover original paintings and artwork. Each piece tells a unique story through color, texture, and emotion.',
  keywords = 'artist, paintings, artwork, gallery, original art, paintings for sale',
  image = '/og-image.jpg',
  url = '',
  type = 'website',
}: SEOProps = {}): Metadata {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const fullUrl = url ? `${baseUrl}${url}` : baseUrl;
  const fullImageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;

  return {
    title,
    description,
    keywords,
    authors: [{ name: 'Artist Portfolio' }],
    creator: 'Artist Portfolio',
    publisher: 'Artist Portfolio',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: fullUrl,
    },
    openGraph: {
      title,
      description,
      url: fullUrl,
      siteName: 'Artist Portfolio',
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: 'en_US',
      type,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [fullImageUrl],
      creator: '@artistportfolio',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

// Generate structured data for artwork
export function generateArtworkStructuredData(artwork: {
  id: number;
  title: string;
  description?: string;
  image_url: string;
  status: 'available' | 'sold';
  created_at: Date;
}) {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'VisualArtwork',
    name: artwork.title,
    description: artwork.description || `Original artwork: ${artwork.title}`,
    image: artwork.image_url,
    url: `${baseUrl}/gallery/${artwork.id}`,
    creator: {
      '@type': 'Person',
      name: 'Artist Portfolio',
    },
    dateCreated: artwork.created_at.toISOString(),
    artMedium: 'Painting',
    artworkSurface: 'Paper',
    offers: artwork.status === 'available' ? {
      '@type': 'Offer',
      availability: 'https://schema.org/InStock',
      priceCurrency: 'USD',
    } : undefined,
  };
}

// Generate structured data for the artist/organization
export function generateArtistStructuredData() {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: 'Artist Portfolio',
    jobTitle: 'Visual Artist',
    description: 'Professional artist specializing in original paintings on paper',
    url: baseUrl,
    sameAs: [
      // Add social media URLs here
    ],
    knowsAbout: ['Painting', 'Visual Arts', 'Fine Arts'],
    hasOccupation: {
      '@type': 'Occupation',
      name: 'Visual Artist',
      occupationLocation: {
        '@type': 'Place',
        name: 'Artist Studio',
      },
    },
  };
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${baseUrl}${item.url}`,
    })),
  };
}