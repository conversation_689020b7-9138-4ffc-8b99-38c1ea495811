@import "tailwindcss";

:root {
  --background: #fefefe;
  --foreground: #2d3748;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-playfair);
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Gallery-specific styles */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Image loading animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Like button animation */
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.heart-animation {
  animation: heartBeat 0.3s ease-in-out;
}

/* Hero section styles */
.hero-bg {
  background-color: #e3d1a4;
}

.life-savers-font {
  font-family: "Life Savers", serif;
}

.libre-bodoni-font {
  font-family: "Libre Bodoni", serif;
}

.poly-font {
  font-family: "Poly", serif;
}

/* Gallery section styles */
.gallery-bg {
  background-color: #e3d1a4;
  background-image: url("/images/hero/gallery-home/background-texture.png");
  background-repeat: repeat;
  background-size: auto;
}

.gallery-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-auto-rows: 180px;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .gallery-masonry {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-auto-rows: 200px;
    gap: 20px;
  }
}

.gallery-item {
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
}

.gallery-item:nth-child(1) {
  grid-row: span 2;
}

.gallery-item:nth-child(2) {
  grid-row: span 2;
}

.gallery-item:nth-child(3) {
  grid-row: span 1;
}

.gallery-item:nth-child(4) {
  grid-row: span 1;
}

.gallery-item:nth-child(5) {
  grid-row: span 2;
}

.gallery-like-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (max-width: 639px) {
  .gallery-masonry {
    grid-template-columns: 1fr;
    grid-auto-rows: 220px;
    gap: 12px;
  }

  .gallery-item:nth-child(n) {
    grid-row: span 1;
  }
}

@media (min-width: 640px) and (max-width: 767px) {
  .gallery-masonry {
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: 200px;
  }

  .gallery-item:nth-child(1),
  .gallery-item:nth-child(2) {
    grid-row: span 2;
  }

  .gallery-item:nth-child(3),
  .gallery-item:nth-child(4),
  .gallery-item:nth-child(5) {
    grid-row: span 1;
  }
}

/* Footer styles */
.footer-bg {
  background-color: #e3d1a4;
}
