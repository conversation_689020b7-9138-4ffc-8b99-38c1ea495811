import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { ArtworkDB } from '@/lib/db';
import { artworkValidation } from '@/lib/validation';
import { deleteImage } from '@/lib/cloudinary';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid artwork ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Validate artwork data
    const validation = artworkValidation.validateUpdate({ id, ...body });
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Sanitize data
    const sanitizedData = artworkValidation.sanitizeUpdate({ id, ...body });

    // Update artwork
    const artwork = await ArtworkDB.update(id, sanitizedData);

    return NextResponse.json({
      success: true,
      data: artwork
    });
  } catch (error) {
    console.error('Failed to update artwork:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update artwork' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid artwork ID' },
        { status: 400 }
      );
    }

    // Get artwork details before deletion (for Cloudinary cleanup)
    const artwork = await ArtworkDB.getById(id);
    if (!artwork) {
      return NextResponse.json(
        { success: false, error: 'Artwork not found' },
        { status: 404 }
      );
    }

    // Delete from database first
    const deletedArtwork = await ArtworkDB.delete(id);
    
    if (!deletedArtwork) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete artwork from database' },
        { status: 500 }
      );
    }

    // Try to delete from Cloudinary (don't fail if this fails)
    try {
      if (artwork.cloudinary_public_id) {
        await deleteImage(artwork.cloudinary_public_id);
      }
    } catch (cloudinaryError) {
      console.warn('Failed to delete image from Cloudinary:', cloudinaryError);
      // Continue anyway - database deletion succeeded
    }

    return NextResponse.json({
      success: true,
      data: deletedArtwork
    });
  } catch (error) {
    console.error('Failed to delete artwork:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete artwork' },
      { status: 500 }
    );
  }
}