import { NextRequest, NextResponse } from 'next/server';
import { ArtworkDB } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid artwork ID' },
        { status: 400 }
      );
    }

    const artwork = await ArtworkDB.getById(id);
    
    if (!artwork) {
      return NextResponse.json(
        { success: false, error: 'Artwork not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: artwork,
    });
  } catch (error) {
    console.error('Failed to fetch artwork:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch artwork' },
      { status: 500 }
    );
  }
}