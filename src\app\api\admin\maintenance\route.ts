import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { DatabaseOptimizer } from '@/lib/db-optimization';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Run database maintenance
    const results = await DatabaseOptimizer.runMaintenance();

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Database maintenance completed successfully'
    });
  } catch (error) {
    console.error('Database maintenance failed:', error);
    return NextResponse.json(
      { success: false, error: 'Database maintenance failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check database health and limits
    const health = await DatabaseOptimizer.checkLimits();

    return NextResponse.json({
      success: true,
      data: health
    });
  } catch (error) {
    console.error('Database health check failed:', error);
    return NextResponse.json(
      { success: false, error: 'Database health check failed' },
      { status: 500 }
    );
  }
}