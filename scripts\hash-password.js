const bcrypt = require('bcryptjs');

// <PERSON>ript to hash admin password for environment variables
async function hashPassword() {
  const password = process.argv[2];
  
  if (!password) {
    console.error('Usage: node scripts/hash-password.js <password>');
    process.exit(1);
  }
  
  try {
    const hashedPassword = await bcrypt.hash(password, 12);
    console.log('Hashed password:');
    console.log(hashedPassword);
    console.log('\nAdd this to your .env.local file:');
    console.log(`ADMIN_PASSWORD="${hashedPassword}"`);
  } catch (error) {
    console.error('Error hashing password:', error);
    process.exit(1);
  }
}

hashPassword();