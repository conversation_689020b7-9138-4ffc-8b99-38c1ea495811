// Configuration validation for external services

export interface ServiceConfig {
  name: string;
  required: boolean;
  configured: boolean;
  variables: string[];
}

export function validateConfiguration(): {
  valid: boolean;
  services: ServiceConfig[];
  errors: string[];
} {
  const services: ServiceConfig[] = [
    {
      name: 'Vercel Postgres',
      required: true,
      configured: false,
      variables: ['POSTGRES_URL'],
    },
    {
      name: 'Cloudinary',
      required: true,
      configured: false,
      variables: ['CLOUDINARY_CLOUD_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET'],
    },
    {
      name: 'NextAuth',
      required: true,
      configured: false,
      variables: ['NEXTAUTH_SECRET', 'NEXTAUTH_URL'],
    },
    {
      name: 'Admin Credentials',
      required: true,
      configured: false,
      variables: ['ADMIN_USERNAME', 'ADMIN_PASSWORD'],
    },
  ];

  const errors: string[] = [];

  // Check each service configuration
  services.forEach(service => {
    const missingVars = service.variables.filter(varName => !process.env[varName]);
    
    if (missingVars.length === 0) {
      service.configured = true;
    } else {
      if (service.required) {
        errors.push(`${service.name}: Missing required environment variables: ${missingVars.join(', ')}`);
      }
    }
  });

  return {
    valid: errors.length === 0,
    services,
    errors,
  };
}

// Get service status for admin dashboard
export function getServiceStatus() {
  const config = validateConfiguration();
  
  return {
    timestamp: new Date(),
    overall: config.valid ? 'healthy' : 'error',
    services: config.services.map(service => ({
      name: service.name,
      status: service.configured ? 'connected' : 'error',
      required: service.required,
    })),
    errors: config.errors,
  };
}

// Validate Vercel free tier limits
export function checkVercelLimits() {
  const warnings: string[] = [];
  
  // These are approximate checks - actual limits may vary
  const limits = {
    functions: {
      executions: 100000, // per month
      duration: 10, // seconds per execution
    },
    bandwidth: {
      monthly: 100 * 1024 * 1024 * 1024, // 100GB
    },
    storage: {
      postgres: 0.5 * 1024 * 1024 * 1024, // 0.5GB
    },
  };

  // Add warnings for approaching limits (this would need actual usage data)
  return {
    limits,
    warnings,
    recommendations: [
      'Monitor Cloudinary usage to stay within 25GB storage/bandwidth',
      'Optimize images to reduce bandwidth usage',
      'Use efficient database queries to minimize compute time',
      'Consider implementing caching to reduce API calls',
    ],
  };
}