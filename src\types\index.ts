// Artwork data model
export interface Artwork {
  id: number;
  title: string;
  description?: string;
  image_url: string;
  cloudinary_public_id: string;
  status: 'available' | 'sold';
  like_count: number;
  created_at: Date;
  updated_at: Date;
  featured: boolean;
  sort_order: number;
}

// Like data model
export interface ArtworkLike {
  id: number;
  artwork_id: number;
  user_ip: string;
  user_agent?: string;
  created_at: Date;
}

// Admin session model
export interface AdminSession {
  id: number;
  session_token: string;
  user_id: string;
  expires_at: Date;
  created_at: Date;
}

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Artwork creation/update types
export interface CreateArtworkData {
  title: string;
  description?: string;
  image_url: string;
  cloudinary_public_id: string;
  status: 'available' | 'sold';
  featured?: boolean;
}

export interface UpdateArtworkData extends Partial<CreateArtworkData> {
  id: number;
}

// Like operation types
export interface LikeOperation {
  artwork_id: number;
  user_ip: string;
  user_agent?: string;
}

// Filter and pagination types
export interface ArtworkFilters {
  status?: 'available' | 'sold' | 'all';
  featured?: boolean;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}