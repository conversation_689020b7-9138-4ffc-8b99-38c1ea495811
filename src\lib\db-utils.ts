import { sql } from '@vercel/postgres';

// Database health check
export async function checkDatabaseHealth() {
  try {
    await sql`SELECT 1 as health_check`;
    return { healthy: true, timestamp: new Date() };
  } catch (error) {
    console.error('Database health check failed:', error);
    return { healthy: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Get database statistics
export async function getDatabaseStats() {
  try {
    const artworkCount = await sql`SELECT COUNT(*) as count FROM artworks`;
    const likeCount = await sql`SELECT COUNT(*) as count FROM artwork_likes`;
    const sessionCount = await sql`SELECT COUNT(*) as count FROM admin_sessions WHERE expires_at > NOW()`;
    
    return {
      artworks: parseInt(artworkCount.rows[0].count),
      likes: parseInt(likeCount.rows[0].count),
      activeSessions: parseInt(sessionCount.rows[0].count),
    };
  } catch (error) {
    console.error('Failed to get database stats:', error);
    return null;
  }
}

// Clean up expired data
export async function cleanupExpiredData() {
  try {
    // Clean expired sessions
    const expiredSessions = await sql`
      DELETE FROM admin_sessions 
      WHERE expires_at <= NOW() 
      RETURNING id
    `;
    
    console.log(`Cleaned up ${expiredSessions.rows.length} expired sessions`);
    
    return {
      expiredSessions: expiredSessions.rows.length,
    };
  } catch (error) {
    console.error('Cleanup failed:', error);
    return null;
  }
}

// Backup artwork data (for development)
export async function backupArtworks() {
  try {
    const artworks = await sql`
      SELECT id, title, description, image_url, cloudinary_public_id, 
             status, like_count, created_at, updated_at, featured, sort_order
      FROM artworks 
      ORDER BY created_at DESC
    `;
    
    return {
      timestamp: new Date(),
      count: artworks.rows.length,
      data: artworks.rows,
    };
  } catch (error) {
    console.error('Backup failed:', error);
    return null;
  }
}