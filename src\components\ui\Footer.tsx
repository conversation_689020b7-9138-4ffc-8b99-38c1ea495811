import Link from "next/link";
import Image from "next/image";

export default function Footer() {
  return (
    <footer className="footer-bg py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
          {/* Left Section - Artist Name */}
          <div className="lg:col-span-1">
            <h2 className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight libre-bodoni-font">
              John
              <br />
              Joseph
            </h2>
          </div>

          {/* Center Section - Social Icons */}
          <div className="lg:col-span-1 flex justify-center">
            <div className="flex space-x-4">
              <a
                href="#"
                className="hover:opacity-80 transition-opacity duration-200"
                aria-label="Instagram"
                title="Follow us on Instagram"
              >
                <Image
                  src="/images/footer/instagram.png"
                  alt="Instagram"
                  width={56}
                  height={56}
                  className="hover:scale-105 transition-transform duration-200"
                />
              </a>
              <a
                href="#"
                className="hover:opacity-80 transition-opacity duration-200"
                aria-label="Facebook"
                title="Follow us on Facebook"
              >
                <Image
                  src="/images/footer/facebook.png"
                  alt="Facebook"
                  width={56}
                  height={56}
                  className="hover:scale-105 transition-transform duration-200"
                />
              </a>
              <a
                href="#"
                className="hover:opacity-80 transition-opacity duration-200"
                aria-label="Contact"
                title="Contact us"
              >
                <Image
                  src="/images/footer/phone.png"
                  alt="Contact"
                  width={56}
                  height={56}
                  className="hover:scale-105 transition-transform duration-200"
                />
              </a>
            </div>
          </div>

          {/* Right Section - Navigation */}
          <div className="lg:col-span-1 flex justify-center lg:justify-end">
            <nav className="flex flex-col space-y-4 text-right">
              <Link
                href="/"
                className="text-gray-700 hover:text-gray-900 text-xl font-medium transition-colors"
              >
                Home
              </Link>
              <Link
                href="/gallery"
                className="text-gray-700 hover:text-gray-900 text-xl font-medium transition-colors"
              >
                Gallery
              </Link>
              <Link
                href="/about"
                className="text-gray-700 hover:text-gray-900 text-xl font-medium transition-colors"
              >
                About
              </Link>
              <Link
                href="/contact"
                className="text-gray-700 hover:text-gray-900 text-xl font-medium transition-colors"
              >
                Contact
              </Link>
            </nav>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 text-lg poly-font">
            © 2025 John Joseph. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
