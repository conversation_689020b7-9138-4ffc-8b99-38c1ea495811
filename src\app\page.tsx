"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Artwork } from "@/types";
import ArtworkCard from "@/components/gallery/ArtworkCard";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

export default function Home() {
  const [featuredArtworks, setFeaturedArtworks] = useState<Artwork[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedArtwork, setSelectedArtwork] = useState<Artwork | null>(null);

  useEffect(() => {
    fetchFeaturedArtworks();
  }, []);

  const fetchFeaturedArtworks = async () => {
    try {
      const response = await fetch("/api/artworks/featured?limit=6");
      const data = await response.json();

      if (data.success) {
        setFeaturedArtworks(data.data);
      }
    } catch (error) {
      console.error("Failed to fetch featured artworks:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (artwork: Artwork) => {
    setSelectedArtwork(artwork);
  };

  const closeModal = () => {
    setSelectedArtwork(null);
  };
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center hero-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8 pt-20 lg:pt-0">
              <div>
                <h1 className="text-6xl md:text-8xl font-bold text-gray-900 leading-tight life-savers-font">
                  John
                  <br />
                  Joseph
                </h1>
                <p className="text-xl md:text-2xl text-gray-700 mt-6 font-medium">
                  Art That Echoes the Unspoken
                </p>
              </div>

              <div className="pt-4">
                <a
                  href="/gallery"
                  className="inline-flex items-center bg-gray-800 text-white px-8 py-4 text-lg font-medium hover:bg-gray-700 transition-colors rounded-full shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200 life-savers-font"
                >
                  <span>Gallery</span>
                  <Image
                    src="/images/hero/gallery.png"
                    alt="Gallery"
                    width={24}
                    height={24}
                    className="ml-3"
                  />
                </a>
              </div>
            </div>

            {/* Right Content - Human Illustration */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <Image
                  src="/images/hero/human.png"
                  alt="Abstract human figures"
                  width={500}
                  height={600}
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section Placeholder */}
      <section id="about" className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-serif font-bold text-gray-900 mb-8">
            About the Artist
          </h2>
          <div className="prose prose-lg mx-auto text-gray-600">
            <p>
              Welcome to my artistic journey. Each painting is a window into my
              soul, capturing moments of inspiration and translating them onto
              paper with passion and precision.
            </p>
            <p>
              My work explores the intersection of emotion and color, creating
              pieces that resonate with viewers on a deeply personal level.
              Every brushstroke tells a story, every composition invites
              contemplation.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Gallery Preview */}
      <section className="py-20 bg-gray-50 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">
              Featured Artwork
            </h2>
            <p className="text-xl text-gray-600">
              A curated selection of my latest and most beloved pieces
            </p>
          </div>

          {/* Featured Artworks Grid */}
          {loading ? (
            <div className="gallery-grid mb-12">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div
                  key={i}
                  className="bg-white rounded-lg shadow-sm overflow-hidden animate-pulse"
                >
                  <div className="aspect-[4/5] bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : featuredArtworks.length > 0 ? (
            <div className="gallery-grid mb-12">
              {featuredArtworks.map((artwork) => (
                <ArtworkCard
                  key={artwork.id}
                  artwork={artwork}
                  onImageClick={handleImageClick}
                  showLikes={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No featured artworks
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Mark some artworks as featured in the admin panel to display
                them here.
              </p>
            </div>
          )}

          <div className="text-center">
            <a
              href="/gallery"
              className="inline-block border-2 border-gray-900 text-gray-900 px-8 py-3 text-lg font-medium hover:bg-gray-900 hover:text-white transition-colors"
            >
              View Full Gallery
            </a>
          </div>
        </div>
      </section>

      {/* Modal for enlarged artwork view */}
      {selectedArtwork && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex">
              {/* Image */}
              <div className="flex-1 relative">
                <Image
                  src={selectedArtwork.image_url}
                  alt={selectedArtwork.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>

              {/* Details */}
              <div className="w-80 p-6 flex flex-col">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-serif font-bold text-gray-900">
                    {selectedArtwork.title}
                  </h2>
                  <button
                    type="button"
                    onClick={closeModal}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    aria-label="Close modal"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                {selectedArtwork.description && (
                  <p className="text-gray-600 mb-4">
                    {selectedArtwork.description}
                  </p>
                )}

                <div className="flex items-center justify-between mb-4">
                  <span
                    className={`
                    inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    ${
                      selectedArtwork.status === "available"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }
                  `}
                  >
                    {selectedArtwork.status === "available"
                      ? "Available"
                      : "Sold"}
                  </span>
                </div>

                <div className="mt-auto">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Created{" "}
                      {new Date(
                        selectedArtwork.created_at
                      ).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
