"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Artwork } from "@/types";
import ArtworkCard from "@/components/gallery/ArtworkCard";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import LikeButton from "@/components/gallery/LikeButton";

export default function Home() {
  const [featuredArtworks, setFeaturedArtworks] = useState<Artwork[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedArtwork, setSelectedArtwork] = useState<Artwork | null>(null);
  const [artworkLikeCounts, setArtworkLikeCounts] = useState<
    Record<number, number>
  >({});

  useEffect(() => {
    fetchFeaturedArtworks();
  }, []);

  const fetchFeaturedArtworks = async () => {
    try {
      const response = await fetch("/api/artworks/featured?limit=6");
      const data = await response.json();

      if (data.success) {
        setFeaturedArtworks(data.data);
        // Initialize like counts
        const likeCounts: Record<number, number> = {};
        data.data.forEach((artwork: Artwork) => {
          likeCounts[artwork.id] = artwork.like_count || 0;
        });
        setArtworkLikeCounts(likeCounts);
      }
    } catch (error) {
      console.error("Failed to fetch featured artworks:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLikeUpdate = (artworkId: number, newCount: number) => {
    setArtworkLikeCounts((prev) => ({
      ...prev,
      [artworkId]: newCount,
    }));
  };

  const handleImageClick = (artwork: Artwork) => {
    setSelectedArtwork(artwork);
  };

  const closeModal = () => {
    setSelectedArtwork(null);
  };
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center hero-bg py-8 lg:py-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-6 lg:space-y-8 pt-16 lg:pt-0 text-center lg:text-left order-2 lg:order-1">
              <div>
                <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-bold text-gray-900 leading-tight life-savers-font">
                  John
                  <br />
                  Joseph
                </h1>
                <p className="text-lg sm:text-xl md:text-2xl text-gray-700 mt-4 lg:mt-6 font-medium px-4 lg:px-0">
                  Art That Echoes the Unspoken
                </p>
              </div>

              <div className="pt-2 lg:pt-4">
                <a
                  href="/gallery"
                  className="inline-block hover:opacity-80 transition-opacity duration-200"
                  title="View Gallery"
                >
                  <Image
                    src="/images/hero/gallery.png"
                    alt="Gallery"
                    width={160}
                    height={48}
                    className="hover:scale-105 transition-transform duration-200 w-[160px] h-[48px] sm:w-[180px] sm:h-[54px] lg:w-[200px] lg:h-[60px]"
                  />
                </a>
              </div>
            </div>

            {/* Right Content - Human Illustration */}
            <div className="flex justify-center lg:justify-end order-1 lg:order-2">
              <div className="relative">
                <Image
                  src="/images/hero/human.png"
                  alt="Abstract human figures"
                  width={280}
                  height={336}
                  className="object-contain w-[280px] h-[336px] sm:w-[320px] sm:h-[384px] md:w-[400px] md:h-[480px] lg:w-[500px] lg:h-[600px]"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Gallery Preview */}
      <section className="py-12 sm:py-16 lg:py-20 gallery-bg px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8 sm:mb-12 lg:mb-16">
            <h2 className="text-4xl sm:text-5xl lg:text-6xl libre-bodoni-font font-bold text-gray-900 mb-4">
              Gallery
            </h2>
          </div>

          {/* Featured Artworks Masonry Grid */}
          {loading ? (
            <div className="gallery-masonry mb-16">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="gallery-item bg-gray-200 animate-pulse">
                  <div className="w-full h-full bg-gray-300"></div>
                </div>
              ))}
            </div>
          ) : featuredArtworks.length > 0 ? (
            <div className="gallery-masonry mb-8 sm:mb-12 lg:mb-16">
              {featuredArtworks.slice(0, 5).map((artwork, index) => (
                <div
                  key={artwork.id}
                  className="gallery-item cursor-pointer group"
                  onClick={() => handleImageClick(artwork)}
                >
                  <Image
                    src={artwork.image_url}
                    alt={artwork.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                    <div className="relative p-4 w-full text-white z-10">
                      <h3 className="text-lg font-serif font-bold mb-2 text-white">
                        {artwork.title}
                      </h3>

                      {artwork.description && (
                        <p className="text-sm text-gray-200 mb-3 line-clamp-2">
                          {artwork.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <span
                          className={`
                            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            ${
                              artwork.status === "available"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }
                          `}
                        >
                          {artwork.status === "available"
                            ? "Available"
                            : "Sold"}
                        </span>

                        <span className="text-xs text-gray-300">
                          {new Date(artwork.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="absolute top-3 right-3 z-10">
                    <LikeButton
                      artworkId={artwork.id}
                      initialCount={
                        artworkLikeCounts[artwork.id] || artwork.like_count || 0
                      }
                      onLikeUpdate={(newCount) =>
                        handleLikeUpdate(artwork.id, newCount)
                      }
                      size="sm"
                      variant="overlay"
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No featured artworks
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Mark some artworks as featured in the admin panel to display
                them here.
              </p>
            </div>
          )}

          <div className="text-center">
            <button
              type="button"
              onClick={() => (window.location.href = "/gallery")}
              className="inline-flex items-center justify-center bg-transparent border-0 p-0 cursor-pointer"
              aria-label="Load more artworks"
            >
              <Image
                src="/images/hero/gallery-home/load-more-btn.png"
                alt="Load More"
                width={120}
                height={40}
                className="hover:opacity-80 transition-opacity w-[120px] h-[40px] sm:w-[135px] sm:h-[45px] lg:w-[150px] lg:h-[50px]"
              />
            </button>
          </div>
        </div>
      </section>

      {/* Modal for enlarged artwork view */}
      {selectedArtwork && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex">
              {/* Image */}
              <div className="flex-1 relative">
                <Image
                  src={selectedArtwork.image_url}
                  alt={selectedArtwork.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>

              {/* Details */}
              <div className="w-80 p-6 flex flex-col">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-serif font-bold text-gray-900">
                    {selectedArtwork.title}
                  </h2>
                  <button
                    type="button"
                    onClick={closeModal}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    aria-label="Close modal"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                {selectedArtwork.description && (
                  <p className="text-gray-600 mb-4">
                    {selectedArtwork.description}
                  </p>
                )}

                <div className="flex items-center justify-between mb-4">
                  <span
                    className={`
                    inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    ${
                      selectedArtwork.status === "available"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }
                  `}
                  >
                    {selectedArtwork.status === "available"
                      ? "Available"
                      : "Sold"}
                  </span>
                </div>

                <div className="mt-auto">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Created{" "}
                      {new Date(
                        selectedArtwork.created_at
                      ).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
