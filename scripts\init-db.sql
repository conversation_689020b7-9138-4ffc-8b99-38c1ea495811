-- Create artworks table
CREATE TABLE IF NOT EXISTS artworks (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  cloudinary_public_id VARCHAR(255) NOT NULL,
  status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'sold')),
  like_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  featured BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0
);

-- Create indexes for artworks table
CREATE INDEX IF NOT EXISTS idx_artworks_status ON artworks(status);
CREATE INDEX IF NOT EXISTS idx_artworks_featured ON artworks(featured);
CREATE INDEX IF NOT EXISTS idx_artworks_created_at ON artworks(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_artworks_sort_order ON artworks(sort_order);

-- Create artwork_likes table
CREATE TABLE IF NOT EXISTS artwork_likes (
  id SERIAL PRIMARY KEY,
  artwork_id INTEGER REFERENCES artworks(id) ON DELETE CASCADE,
  user_ip VARCHAR(45) NOT NULL,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(artwork_id, user_ip)
);

-- Create indexes for artwork_likes table
CREATE INDEX IF NOT EXISTS idx_likes_artwork_id ON artwork_likes(artwork_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_ip ON artwork_likes(user_ip);

-- Create admin_sessions table
CREATE TABLE IF NOT EXISTS admin_sessions (
  id SERIAL PRIMARY KEY,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  user_id VARCHAR(100) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for admin_sessions table
CREATE INDEX IF NOT EXISTS idx_sessions_token ON admin_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON admin_sessions(expires_at);

-- Insert sample data for development (optional)
INSERT INTO artworks (title, description, image_url, cloudinary_public_id, status, featured, sort_order) 
VALUES 
  ('Sample Painting 1', 'A beautiful landscape painting', 'https://via.placeholder.com/600x800', 'sample_1', 'available', true, 1),
  ('Sample Painting 2', 'An abstract composition', 'https://via.placeholder.com/600x600', 'sample_2', 'available', true, 2),
  ('Sample Painting 3', 'A portrait study', 'https://via.placeholder.com/500x700', 'sample_3', 'sold', false, 3)
ON CONFLICT DO NOTHING;