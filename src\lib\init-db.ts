import { sql } from '@vercel/postgres';
import fs from 'fs';
import path from 'path';

export async function initializeDatabase() {
  try {
    console.log('Initializing database...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'scripts', 'init-db.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split by semicolons and execute each statement
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    for (const statement of statements) {
      if (statement.trim()) {
        await sql.query(statement);
        console.log('Executed:', statement.substring(0, 50) + '...');
      }
    }
    
    console.log('Database initialized successfully!');
    return { success: true };
  } catch (error) {
    console.error('Database initialization failed:', error);
    return { success: false, error };
  }
}

// Test database connection
export async function testConnection() {
  try {
    const result = await sql`SELECT NOW() as current_time`;
    console.log('Database connection successful:', result.rows[0]);
    return { success: true, data: result.rows[0] };
  } catch (error) {
    console.error('Database connection failed:', error);
    return { success: false, error };
  }
}