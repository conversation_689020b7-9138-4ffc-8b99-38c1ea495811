'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Artwork } from '@/types';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function ManageGalleryPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [artworks, setArtworks] = useState<Artwork[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/admin/login');
      return;
    }
    fetchArtworks();
  }, [session, status, router]);

  const fetchArtworks = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/artworks');
      const data = await response.json();
      
      if (data.success) {
        setArtworks(data.data);
      } else {
        setError(data.error || 'Failed to load artworks');
      }
    } catch (err) {
      setError('Failed to load artworks');
      console.error('Error fetching artworks:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusToggle = async (id: number, currentStatus: 'available' | 'sold') => {
    try {
      const newStatus = currentStatus === 'available' ? 'sold' : 'available';
      
      const response = await fetch(`/api/admin/artworks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();
      
      if (data.success) {
        setArtworks(prev => 
          prev.map(artwork => 
            artwork.id === id 
              ? { ...artwork, status: newStatus }
              : artwork
          )
        );
      } else {
        alert('Failed to update status: ' + data.error);
      }
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Failed to update status');
    }
  };

  const handleFeaturedToggle = async (id: number, currentFeatured: boolean) => {
    try {
      const response = await fetch(`/api/admin/artworks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ featured: !currentFeatured }),
      });

      const data = await response.json();
      
      if (data.success) {
        setArtworks(prev => 
          prev.map(artwork => 
            artwork.id === id 
              ? { ...artwork, featured: !currentFeatured }
              : artwork
          )
        );
      } else {
        alert('Failed to update featured status: ' + data.error);
      }
    } catch (error) {
      console.error('Error updating featured status:', error);
      alert('Failed to update featured status');
    }
  };

  const handleDelete = async (id: number, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeletingId(id);
      
      const response = await fetch(`/api/admin/artworks/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        setArtworks(prev => prev.filter(artwork => artwork.id !== id));
      } else {
        alert('Failed to delete artwork: ' + data.error);
      }
    } catch (error) {
      console.error('Error deleting artwork:', error);
      alert('Failed to delete artwork');
    } finally {
      setDeletingId(null);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Artworks</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={fetchArtworks}
            className="bg-gray-900 text-white px-6 py-2 rounded-md hover:bg-gray-800 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-serif font-bold text-gray-900">
                Manage Gallery
              </h1>
              <p className="text-gray-600 mt-2">
                Edit, update status, and manage your artwork collection
              </p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => router.push('/admin/add-artwork')}
                className="bg-gray-900 text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors"
              >
                Add New Artwork
              </button>
              <button
                onClick={() => router.push('/admin/dashboard')}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>

        {/* Artworks Grid */}
        {artworks.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No artworks found</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by adding your first artwork.</p>
            <div className="mt-6">
              <button
                onClick={() => router.push('/admin/add-artwork')}
                className="bg-gray-900 text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors"
              >
                Add New Artwork
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {artworks.map((artwork) => (
              <div key={artwork.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                {/* Image */}
                <div className="aspect-[4/5] relative">
                  <Image
                    src={artwork.image_url}
                    alt={artwork.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  {artwork.featured && (
                    <div className="absolute top-2 left-2">
                      <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                        Featured
                      </span>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">{artwork.title}</h3>
                  {artwork.description && (
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {artwork.description}
                    </p>
                  )}

                  {/* Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        {artwork.like_count}
                      </span>
                      <span>{new Date(artwork.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="space-y-3">
                    {/* Status Toggle */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Status:</span>
                      <button
                        onClick={() => handleStatusToggle(artwork.id, artwork.status)}
                        className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                          artwork.status === 'available'
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-red-100 text-red-800 hover:bg-red-200'
                        }`}
                      >
                        {artwork.status === 'available' ? 'Available' : 'Sold'}
                      </button>
                    </div>

                    {/* Featured Toggle */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Featured:</span>
                      <button
                        onClick={() => handleFeaturedToggle(artwork.id, artwork.featured)}
                        className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                          artwork.featured
                            ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {artwork.featured ? 'Featured' : 'Not Featured'}
                      </button>
                    </div>

                    {/* Delete Button */}
                    <button
                      onClick={() => handleDelete(artwork.id, artwork.title)}
                      disabled={deletingId === artwork.id}
                      className="w-full bg-red-50 text-red-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {deletingId === artwork.id ? 'Deleting...' : 'Delete Artwork'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}