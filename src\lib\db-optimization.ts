import { sql } from '@vercel/postgres';

// Database optimization utilities for Neon free tier
export class DatabaseOptimizer {
  
  // Clean up expired admin sessions (runs automatically)
  static async cleanupExpiredSessions() {
    try {
      const result = await sql`
        DELETE FROM admin_sessions 
        WHERE expires_at < NOW()
        RETURNING id
      `;
      
      console.log(`Cleaned up ${result.rows.length} expired sessions`);
      return result.rows.length;
    } catch (error) {
      console.error('Session cleanup failed:', error);
      return 0;
    }
  }

  // Remove duplicate likes (shouldn't happen, but safety measure)
  static async cleanupDuplicateLikes() {
    try {
      const result = await sql`
        DELETE FROM artwork_likes a
        USING artwork_likes b
        WHERE a.id > b.id 
        AND a.artwork_id = b.artwork_id 
        AND a.user_ip = b.user_ip
        RETURNING a.id
      `;
      
      console.log(`Cleaned up ${result.rows.length} duplicate likes`);
      return result.rows.length;
    } catch (error) {
      console.error('Duplicate likes cleanup failed:', error);
      return 0;
    }
  }

  // Get database size information
  static async getDatabaseSize() {
    try {
      const artworkCount = await sql`SELECT COUNT(*) as count FROM artworks`;
      const likeCount = await sql`SELECT COUNT(*) as count FROM artwork_likes`;
      const sessionCount = await sql`SELECT COUNT(*) as count FROM admin_sessions`;
      
      // Estimate storage usage (rough calculation)
      const artworkSize = parseInt(artworkCount.rows[0].count) * 1024; // ~1KB per artwork
      const likeSize = parseInt(likeCount.rows[0].count) * 64; // ~64 bytes per like
      const sessionSize = parseInt(sessionCount.rows[0].count) * 256; // ~256 bytes per session
      
      const totalSize = artworkSize + likeSize + sessionSize;
      
      return {
        artworks: parseInt(artworkCount.rows[0].count),
        likes: parseInt(likeCount.rows[0].count),
        sessions: parseInt(sessionCount.rows[0].count),
        estimatedSize: {
          bytes: totalSize,
          kb: Math.round(totalSize / 1024),
          mb: Math.round(totalSize / (1024 * 1024) * 100) / 100,
          percentOfFreeLimit: Math.round((totalSize / (0.5 * 1024 * 1024 * 1024)) * 10000) / 100
        }
      };
    } catch (error) {
      console.error('Failed to get database size:', error);
      return null;
    }
  }

  // Optimize like counts (recalculate from actual likes)
  static async optimizeLikeCounts() {
    try {
      const result = await sql`
        UPDATE artworks 
        SET like_count = (
          SELECT COUNT(*) 
          FROM artwork_likes 
          WHERE artwork_likes.artwork_id = artworks.id
        )
        WHERE like_count != (
          SELECT COUNT(*) 
          FROM artwork_likes 
          WHERE artwork_likes.artwork_id = artworks.id
        )
        RETURNING id, title, like_count
      `;
      
      console.log(`Optimized like counts for ${result.rows.length} artworks`);
      return result.rows;
    } catch (error) {
      console.error('Like count optimization failed:', error);
      return [];
    }
  }

  // Run all optimizations
  static async runMaintenance() {
    console.log('Starting database maintenance...');
    
    const results = {
      expiredSessions: await this.cleanupExpiredSessions(),
      duplicateLikes: await this.cleanupDuplicateLikes(),
      optimizedLikes: await this.optimizeLikeCounts(),
      size: await this.getDatabaseSize()
    };
    
    console.log('Database maintenance completed:', results);
    return results;
  }

  // Check if approaching free tier limits
  static async checkLimits() {
    const size = await this.getDatabaseSize();
    if (!size) return null;

    const warnings = [];
    
    // Storage warnings
    if (size.estimatedSize.percentOfFreeLimit > 80) {
      warnings.push('Database approaching 80% of free tier storage limit');
    }
    
    // Record count warnings
    if (size.artworks > 500) {
      warnings.push('Consider archiving old artworks if no longer needed');
    }
    
    if (size.likes > 10000) {
      warnings.push('Consider implementing like count caching to reduce database size');
    }
    
    return {
      size,
      warnings,
      recommendations: warnings.length > 0 ? [
        'Run database maintenance regularly',
        'Consider upgrading to Neon Scale if consistently over limits',
        'Archive old data if possible'
      ] : ['Database usage is healthy']
    };
  }
}

// Utility to run maintenance automatically
export async function runDatabaseMaintenance() {
  return DatabaseOptimizer.runMaintenance();
}

// Utility to check database health
export async function checkDatabaseHealth() {
  return DatabaseOptimizer.checkLimits();
}