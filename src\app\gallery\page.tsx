'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { Artwork } from '@/types';
import ArtworkCard from '@/components/gallery/ArtworkCard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

// This would normally be generated server-side, but for client component we'll do it in useEffect
export default function GalleryPage() {
  const [artworks, setArtworks] = useState<Artwork[]>([]);
  const [filteredArtworks, setFilteredArtworks] = useState<Artwork[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<'all' | 'available' | 'sold'>('all');
  const [selectedArtwork, setSelectedArtwork] = useState<Artwork | null>(null);

  const filterArtworks = useCallback(() => {
    if (activeFilter === 'all') {
      setFilteredArtworks(artworks);
    } else {
      setFilteredArtworks(artworks.filter(artwork => artwork.status === activeFilter));
    }
  }, [artworks, activeFilter]);

  useEffect(() => {
    fetchArtworks();
  }, []);

  useEffect(() => {
    filterArtworks();
  }, [artworks, activeFilter, filterArtworks]);

  const fetchArtworks = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/artworks');
      const data = await response.json();
      
      if (data.success) {
        setArtworks(data.data);
      } else {
        setError(data.error || 'Failed to load artworks');
      }
    } catch (err) {
      setError('Failed to load artworks');
      console.error('Error fetching artworks:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filter: 'all' | 'available' | 'sold') => {
    setActiveFilter(filter);
  };

  const handleImageClick = (artwork: Artwork) => {
    setSelectedArtwork(artwork);
  };

  const closeModal = () => {
    setSelectedArtwork(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Gallery</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={fetchArtworks}
            className="bg-gray-900 text-white px-6 py-2 rounded-md hover:bg-gray-800 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-serif font-bold text-gray-900 mb-4">
              Gallery
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Explore my collection of original paintings. Each piece tells a unique story through color, texture, and emotion.
            </p>
          </div>
        </div>
      </div>

      {/* Filter Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-center space-x-1">
            {[
              { key: 'all', label: 'All Artworks', count: artworks.length },
              { key: 'available', label: 'Available', count: artworks.filter(a => a.status === 'available').length },
              { key: 'sold', label: 'Sold', count: artworks.filter(a => a.status === 'sold').length }
            ].map((filter) => (
              <button
                key={filter.key}
                onClick={() => handleFilterChange(filter.key as any)}
                className={`
                  px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200
                  ${activeFilter === filter.key
                    ? 'bg-gray-900 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }
                `}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Gallery Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {filteredArtworks.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No artworks found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {activeFilter === 'all' 
                ? 'No artworks have been added yet.' 
                : `No ${activeFilter} artworks found.`
              }
            </p>
          </div>
        ) : (
          <div className="gallery-grid">
            {filteredArtworks.map((artwork) => (
              <ArtworkCard
                key={artwork.id}
                artwork={artwork}
                onImageClick={handleImageClick}
                showLikes={true}
              />
            ))}
          </div>
        )}
      </div>

      {/* Modal for enlarged artwork view */}
      {selectedArtwork && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex">
              {/* Image */}
              <div className="flex-1 relative">
                <Image
                  src={selectedArtwork.image_url}
                  alt={selectedArtwork.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
              
              {/* Details */}
              <div className="w-80 p-6 flex flex-col">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-serif font-bold text-gray-900">
                    {selectedArtwork.title}
                  </h2>
                  <button
                    onClick={closeModal}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                {selectedArtwork.description && (
                  <p className="text-gray-600 mb-4">
                    {selectedArtwork.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between mb-4">
                  <span className={`
                    inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    ${selectedArtwork.status === 'available' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                    }
                  `}>
                    {selectedArtwork.status === 'available' ? 'Available' : 'Sold'}
                  </span>
                </div>
                
                <div className="mt-auto">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Created {new Date(selectedArtwork.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}