'use client';

import Image from 'next/image';

export default function About() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-20 hero-bg px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div>
                <h1 className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight life-savers-font">
                  About <PERSON>
                </h1>
                <p className="text-xl md:text-2xl text-gray-700 mt-6 font-medium">
                  Artist, Visionary, Storyteller
                </p>
              </div>
            </div>

            {/* Right Content - Artist Image */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <Image
                  src="/images/hero/human.png"
                  alt="<PERSON> - Artist"
                  width={400}
                  height={500}
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Artist Story Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-8">
            <h2 className="text-4xl libre-bodoni-font font-bold text-gray-900 text-center mb-12">
              My Artistic Journey
            </h2>
            
            <div className="prose prose-lg mx-auto text-gray-600 space-y-6">
              <p>
                Welcome to my artistic journey. Each painting is a window into my soul, 
                capturing moments of inspiration and translating them onto canvas with 
                passion and precision. My work is born from the belief that art has the 
                power to speak the unspoken, to give voice to emotions that words cannot express.
              </p>
              
              <p>
                My artistic exploration began over two decades ago, rooted in a deep fascination 
                with the interplay between light and shadow, color and form. I draw inspiration 
                from the natural world, human emotions, and the quiet moments that often go 
                unnoticed in our busy lives.
              </p>
              
              <p>
                My work explores the intersection of emotion and color, creating pieces 
                that resonate with viewers on a deeply personal level. Every brushstroke 
                tells a story, every composition invites contemplation. I believe that art 
                should not just be observed, but felt—it should stir something within the 
                viewer and create a lasting connection.
              </p>
              
              <p>
                Through my paintings, I aim to create a dialogue between the artwork and the 
                observer, where each piece becomes a mirror reflecting the viewer's own 
                experiences and emotions. This is what I call "Art That Echoes the Unspoken"—
                the ability of visual art to communicate what lies beyond words.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Philosophy Section */}
      <section className="py-20 px-4 gallery-bg">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl libre-bodoni-font font-bold text-gray-900 mb-6">
                Artistic Philosophy
              </h3>
              <div className="space-y-4 text-gray-700">
                <p>
                  I believe that true art transcends mere representation. It captures the essence 
                  of a moment, the feeling of a place, the soul of a subject. My approach to 
                  painting is both intuitive and deliberate—allowing the canvas to guide me 
                  while maintaining a clear vision of the story I want to tell.
                </p>
                <p>
                  Each piece begins with an emotion, a memory, or a fleeting moment that demands 
                  to be preserved. Through careful observation and passionate execution, I transform 
                  these inspirations into visual narratives that speak to the universal human experience.
                </p>
              </div>
            </div>
            
            <div>
              <h3 className="text-3xl libre-bodoni-font font-bold text-gray-900 mb-6">
                Technique & Medium
              </h3>
              <div className="space-y-4 text-gray-700">
                <p>
                  Working primarily with oils and acrylics, I employ a variety of techniques 
                  ranging from traditional realism to contemporary abstract expressionism. 
                  My process often involves multiple layers, building depth and complexity 
                  that mirrors the multifaceted nature of human emotion.
                </p>
                <p>
                  I am particularly drawn to the interplay of texture and color, using both 
                  bold strokes and delicate details to create works that invite close 
                  examination while maintaining their impact from a distance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-3xl libre-bodoni-font font-bold text-gray-900 mb-8">
            Connect With Me
          </h3>
          <p className="text-lg text-gray-600 mb-8">
            I welcome conversations about art, commissions, and collaborations. 
            Feel free to reach out to discuss how we might work together.
          </p>
          <div className="space-y-4">
            <p className="text-gray-700">
              <strong>Email:</strong> <EMAIL>
            </p>
            <p className="text-gray-700">
              <strong>Studio:</strong> Available for visits by appointment
            </p>
            <div className="pt-6">
              <a
                href="/gallery"
                className="inline-flex items-center bg-gray-800 text-white px-8 py-4 text-lg font-medium hover:bg-gray-700 transition-colors rounded-full shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200 life-savers-font"
              >
                View My Work
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
