import { NextRequest, NextResponse } from 'next/server';
import { ArtworkDB } from '@/lib/db';
import { filterValidation } from '@/lib/validation';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filters = {
      status: searchParams.get('status') || undefined,
      featured: searchParams.get('featured') ? searchParams.get('featured') === 'true' : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
    };

    // Validate filters
    const validation = filterValidation.validateFilters(filters);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Sanitize filters
    const sanitizedFilters = filterValidation.sanitizeFilters(filters);

    // Get artworks
    const artworks = await ArtworkDB.getAll(sanitizedFilters);

    return NextResponse.json({
      success: true,
      data: artworks,
    });
  } catch (error) {
    console.error('Failed to fetch artworks:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch artworks' },
      { status: 500 }
    );
  }
}