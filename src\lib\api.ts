import { Artwork, ApiResponse, ArtworkFilters, CreateArtworkData, UpdateArtworkData } from '@/types';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const url = `${API_BASE_URL}/api${endpoint}`;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const data = await response.json();
    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('API request failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error',
    };
  }
}

// Artwork API functions
export const artworkApi = {
  // Get all artworks with optional filtering
  async getAll(filters: ArtworkFilters = {}): Promise<ApiResponse<Artwork[]>> {
    const params = new URLSearchParams();
    
    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters.featured !== undefined) {
      params.append('featured', filters.featured.toString());
    }
    if (filters.limit) {
      params.append('limit', filters.limit.toString());
    }
    if (filters.offset) {
      params.append('offset', filters.offset.toString());
    }

    const queryString = params.toString();
    const endpoint = `/artworks${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest<Artwork[]>(endpoint);
  },

  // Get single artwork by ID
  async getById(id: number): Promise<ApiResponse<Artwork>> {
    return apiRequest<Artwork>(`/artworks/${id}`);
  },

  // Get featured artworks for homepage
  async getFeatured(limit: number = 8): Promise<ApiResponse<Artwork[]>> {
    return apiRequest<Artwork[]>(`/artworks/featured?limit=${limit}`);
  },

  // Like an artwork
  async like(id: number): Promise<ApiResponse<{ likeCount: number; isLiked: boolean }>> {
    return apiRequest<{ likeCount: number; isLiked: boolean }>(`/artworks/${id}/like`, {
      method: 'POST',
    });
  },

  // Admin functions (require authentication)
  async create(data: CreateArtworkData): Promise<ApiResponse<Artwork>> {
    return apiRequest<Artwork>('/admin/artworks', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  async update(data: UpdateArtworkData): Promise<ApiResponse<Artwork>> {
    return apiRequest<Artwork>(`/admin/artworks/${data.id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  async delete(id: number): Promise<ApiResponse<void>> {
    return apiRequest<void>(`/admin/artworks/${id}`, {
      method: 'DELETE',
    });
  },
};

// Image upload API
export const uploadApi = {
  async uploadImage(file: File): Promise<ApiResponse<{ url: string; publicId: string }>> {
    const formData = new FormData();
    formData.append('image', file);

    try {
      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.error || 'Upload failed',
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error('Upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  },
};

// Admin API functions
export const adminApi = {
  async getStats(): Promise<ApiResponse<{
    totalArtworks: number;
    totalLikes: number;
    availableArtworks: number;
    soldArtworks: number;
  }>> {
    return apiRequest('/admin/stats');
  },

  async getAnalytics(): Promise<ApiResponse<{
    popularArtworks: Array<{ id: number; title: string; likeCount: number }>;
    recentActivity: Array<{ type: string; timestamp: Date; details: string }>;
  }>> {
    return apiRequest('/admin/analytics');
  },
};

// Client-side utilities
export const clientUtils = {
  // Debounced API calls to prevent spam
  debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
    let timeout: NodeJS.Timeout;
    return ((...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    }) as T;
  },

  // Retry failed requests
  async retryRequest<T>(
    requestFn: () => Promise<ApiResponse<T>>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<ApiResponse<T>> {
    let lastError: string = '';
    
    for (let i = 0; i <= maxRetries; i++) {
      const result = await requestFn();
      
      if (result.success) {
        return result;
      }
      
      lastError = result.error || 'Unknown error';
      
      if (i < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
    
    return {
      success: false,
      error: `Failed after ${maxRetries + 1} attempts: ${lastError}`,
    };
  },

  // Format error messages for user display
  formatError(error: string): string {
    if (error.includes('Network error')) {
      return 'Please check your internet connection and try again.';
    }
    if (error.includes('HTTP 404')) {
      return 'The requested item was not found.';
    }
    if (error.includes('HTTP 500')) {
      return 'Server error. Please try again later.';
    }
    return error;
  },
};