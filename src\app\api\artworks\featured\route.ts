import { NextRequest, NextResponse } from 'next/server';
import { ArtworkDB } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 8;

    // Get featured artworks
    const artworks = await ArtworkDB.getFeatured(limit);

    return NextResponse.json({
      success: true,
      data: artworks,
    });
  } catch (error) {
    console.error('Failed to fetch featured artworks:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch featured artworks' },
      { status: 500 }
    );
  }
}