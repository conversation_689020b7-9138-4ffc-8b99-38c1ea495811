import { CreateArtworkData, UpdateArtworkData } from '@/types';

// Validation result interface
interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Artwork validation functions
export const artworkValidation = {
  // Validate artwork creation data
  validateCreate(data: Partial<CreateArtworkData>): ValidationResult {
    const errors: string[] = [];

    // Title validation
    if (!data.title || typeof data.title !== 'string') {
      errors.push('Title is required');
    } else if (data.title.trim().length < 1) {
      errors.push('Title cannot be empty');
    } else if (data.title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }

    // Image URL validation
    if (!data.image_url || typeof data.image_url !== 'string') {
      errors.push('Image URL is required');
    } else if (!this.isValidUrl(data.image_url)) {
      errors.push('Invalid image URL format');
    }

    // Cloudinary public ID validation
    if (!data.cloudinary_public_id || typeof data.cloudinary_public_id !== 'string') {
      errors.push('Cloudinary public ID is required');
    }

    // Status validation
    if (!data.status) {
      errors.push('Status is required');
    } else if (!['available', 'sold'].includes(data.status)) {
      errors.push('Status must be either "available" or "sold"');
    }

    // Description validation (optional)
    if (data.description && typeof data.description !== 'string') {
      errors.push('Description must be a string');
    } else if (data.description && data.description.length > 1000) {
      errors.push('Description must be less than 1000 characters');
    }

    // Featured validation (optional)
    if (data.featured !== undefined && typeof data.featured !== 'boolean') {
      errors.push('Featured must be a boolean');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Validate artwork update data
  validateUpdate(data: Partial<UpdateArtworkData>): ValidationResult {
    const errors: string[] = [];

    // ID validation
    if (!data.id || typeof data.id !== 'number' || data.id <= 0) {
      errors.push('Valid artwork ID is required');
    }

    // Title validation (if provided)
    if (data.title !== undefined) {
      if (typeof data.title !== 'string') {
        errors.push('Title must be a string');
      } else if (data.title.trim().length < 1) {
        errors.push('Title cannot be empty');
      } else if (data.title.length > 255) {
        errors.push('Title must be less than 255 characters');
      }
    }

    // Image URL validation (if provided)
    if (data.image_url !== undefined) {
      if (typeof data.image_url !== 'string') {
        errors.push('Image URL must be a string');
      } else if (!this.isValidUrl(data.image_url)) {
        errors.push('Invalid image URL format');
      }
    }

    // Cloudinary public ID validation (if provided)
    if (data.cloudinary_public_id !== undefined && typeof data.cloudinary_public_id !== 'string') {
      errors.push('Cloudinary public ID must be a string');
    }

    // Status validation (if provided)
    if (data.status !== undefined && !['available', 'sold'].includes(data.status)) {
      errors.push('Status must be either "available" or "sold"');
    }

    // Description validation (if provided)
    if (data.description !== undefined) {
      if (typeof data.description !== 'string') {
        errors.push('Description must be a string');
      } else if (data.description.length > 1000) {
        errors.push('Description must be less than 1000 characters');
      }
    }

    // Featured validation (if provided)
    if (data.featured !== undefined && typeof data.featured !== 'boolean') {
      errors.push('Featured must be a boolean');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // URL validation helper
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // Sanitize artwork data
  sanitizeCreate(data: any): CreateArtworkData {
    return {
      title: String(data.title || '').trim(),
      description: data.description ? String(data.description).trim() : undefined,
      image_url: String(data.image_url || '').trim(),
      cloudinary_public_id: String(data.cloudinary_public_id || '').trim(),
      status: data.status === 'sold' ? 'sold' : 'available',
      featured: Boolean(data.featured),
    };
  },

  sanitizeUpdate(data: any): Partial<UpdateArtworkData> {
    const sanitized: Partial<UpdateArtworkData> = {
      id: Number(data.id),
    };

    if (data.title !== undefined) {
      sanitized.title = String(data.title).trim();
    }
    if (data.description !== undefined) {
      sanitized.description = data.description ? String(data.description).trim() : undefined;
    }
    if (data.image_url !== undefined) {
      sanitized.image_url = String(data.image_url).trim();
    }
    if (data.cloudinary_public_id !== undefined) {
      sanitized.cloudinary_public_id = String(data.cloudinary_public_id).trim();
    }
    if (data.status !== undefined) {
      sanitized.status = data.status === 'sold' ? 'sold' : 'available';
    }
    if (data.featured !== undefined) {
      sanitized.featured = Boolean(data.featured);
    }

    return sanitized;
  },
};

// Filter validation
export const filterValidation = {
  validateFilters(filters: any): ValidationResult {
    const errors: string[] = [];

    // Status validation
    if (filters.status !== undefined && !['available', 'sold', 'all'].includes(filters.status)) {
      errors.push('Status filter must be "available", "sold", or "all"');
    }

    // Featured validation
    if (filters.featured !== undefined && typeof filters.featured !== 'boolean') {
      errors.push('Featured filter must be a boolean');
    }

    // Limit validation
    if (filters.limit !== undefined) {
      const limit = Number(filters.limit);
      if (isNaN(limit) || limit < 1 || limit > 100) {
        errors.push('Limit must be a number between 1 and 100');
      }
    }

    // Offset validation
    if (filters.offset !== undefined) {
      const offset = Number(filters.offset);
      if (isNaN(offset) || offset < 0) {
        errors.push('Offset must be a non-negative number');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  sanitizeFilters(filters: any) {
    const sanitized: any = {};

    if (filters.status && ['available', 'sold', 'all'].includes(filters.status)) {
      sanitized.status = filters.status;
    }
    if (filters.featured !== undefined) {
      sanitized.featured = Boolean(filters.featured);
    }
    if (filters.limit !== undefined) {
      const limit = Number(filters.limit);
      if (!isNaN(limit) && limit >= 1 && limit <= 100) {
        sanitized.limit = limit;
      }
    }
    if (filters.offset !== undefined) {
      const offset = Number(filters.offset);
      if (!isNaN(offset) && offset >= 0) {
        sanitized.offset = offset;
      }
    }

    return sanitized;
  },
};

// General validation utilities
export const validationUtils = {
  // Check if string is empty or only whitespace
  isEmpty(value: string): boolean {
    return !value || value.trim().length === 0;
  },

  // Check if value is a positive integer
  isPositiveInteger(value: any): boolean {
    const num = Number(value);
    return Number.isInteger(num) && num > 0;
  },

  // Check if value is a non-negative integer
  isNonNegativeInteger(value: any): boolean {
    const num = Number(value);
    return Number.isInteger(num) && num >= 0;
  },

  // Escape HTML to prevent XSS
  escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  // Validate email format
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
};