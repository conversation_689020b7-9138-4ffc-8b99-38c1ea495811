'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Artwork } from '@/types';
import LikeButton from './LikeButton';

interface ArtworkCardProps {
  artwork: Artwork;
  onImageClick?: (artwork: Artwork) => void;
  showLikes?: boolean;
}

export default function ArtworkCard({ 
  artwork, 
  onImageClick, 
  showLikes = true 
}: ArtworkCardProps) {
  const [likeCount, setLikeCount] = useState(artwork.like_count);
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageClick = () => {
    if (onImageClick) {
      onImageClick(artwork);
    }
  };

  const handleLikeUpdate = (newCount: number) => {
    setLikeCount(newCount);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
      {/* Image */}
      <div 
        className="relative aspect-[4/5] cursor-pointer group"
        onClick={handleImageClick}
      >
{imageError ? (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <svg className="mx-auto h-12 w-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-sm">Image not available</p>
            </div>
          </div>
        ) : (
          <>
            {imageLoading && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="text-gray-400">Loading...</div>
              </div>
            )}
            <Image
              src={artwork.image_url}
              alt={artwork.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              onLoad={handleImageLoad}
              onError={handleImageError}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </>
        )}
        
        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <StatusBadge status={artwork.status} />
        </div>


      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">
          {artwork.title}
        </h3>
        
        {artwork.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {artwork.description}
          </p>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <StatusBadge status={artwork.status} size="sm" />
          </div>
          
          {showLikes && (
            <LikeButton
              artworkId={artwork.id}
              initialCount={likeCount}
              onLikeUpdate={handleLikeUpdate}
              size="sm"
              variant="default"
            />
          )}
        </div>
      </div>
    </div>
  );
}

// Status Badge Component
interface StatusBadgeProps {
  status: 'available' | 'sold';
  size?: 'sm' | 'md';
}

function StatusBadge({ status, size = 'md' }: StatusBadgeProps) {
  const baseClasses = "inline-flex items-center font-medium rounded-full";
  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1 text-sm",
  };
  
  const statusClasses = {
    available: "bg-green-100 text-green-800",
    sold: "bg-red-100 text-red-800",
  };

  return (
    <span className={`${baseClasses} ${sizeClasses[size]} ${statusClasses[status]}`}>
      {status === 'available' ? 'Available' : 'Sold'}
    </span>
  );
}