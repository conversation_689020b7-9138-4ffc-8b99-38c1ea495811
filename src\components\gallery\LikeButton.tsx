'use client';

import { useState, useEffect } from 'react';

interface LikeButtonProps {
  artworkId: number;
  initialCount: number;
  initialIsLiked?: boolean;
  onLikeUpdate?: (newCount: number) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'overlay';
}

export default function LikeButton({
  artworkId,
  initialCount,
  initialIsLiked = false,
  onLikeUpdate,
  size = 'md',
  variant = 'default'
}: LikeButtonProps) {
  const [likeCount, setLikeCount] = useState(initialCount);
  const [isLiked, setIsLiked] = useState(initialIsLiked);
  const [isLoading, setIsLoading] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Check initial like status on mount
  useEffect(() => {
    const checkLikeStatus = async () => {
      try {
        const response = await fetch(`/api/artworks/${artworkId}/like`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setIsLiked(data.data.isLiked);
            setLikeCount(data.data.likeCount);
          }
        }
      } catch (error) {
        console.error('Failed to check like status:', error);
      }
    };
    
    checkLikeStatus();
  }, [artworkId]);

  const handleLike = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isLoading) return;

    setIsLoading(true);
    setIsAnimating(true);

    try {
      const response = await fetch(`/api/artworks/${artworkId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const newCount = data.data.likeCount;
          const newIsLiked = data.data.isLiked;
          
          setLikeCount(newCount);
          setIsLiked(newIsLiked);
          
          // Notify parent component
          if (onLikeUpdate) {
            onLikeUpdate(newCount);
          }
        }
      } else {
        console.error('Failed to toggle like');
      }
    } catch (error) {
      console.error('Like operation failed:', error);
    } finally {
      setIsLoading(false);
      // Remove animation class after animation completes
      setTimeout(() => setIsAnimating(false), 300);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: {
      button: 'p-1.5',
      icon: 'w-4 h-4',
      text: 'text-xs'
    },
    md: {
      button: 'p-2',
      icon: 'w-5 h-5',
      text: 'text-sm'
    },
    lg: {
      button: 'p-3',
      icon: 'w-6 h-6',
      text: 'text-base'
    }
  };

  // Variant classes
  const variantClasses = {
    default: 'bg-white border border-gray-200 hover:border-gray-300 shadow-sm',
    overlay: 'bg-white/90 backdrop-blur-sm shadow-sm hover:bg-white'
  };

  const currentSize = sizeClasses[size];
  const currentVariant = variantClasses[variant];

  return (
    <button
      onClick={handleLike}
      disabled={isLoading}
      className={`
        ${currentSize.button} ${currentVariant}
        inline-flex items-center space-x-1 rounded-full
        transition-all duration-200 ease-in-out
        hover:scale-105 active:scale-95
        disabled:opacity-50 disabled:cursor-not-allowed
        focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2
        ${isAnimating ? 'heart-animation' : ''}
      `}
      title={isLiked ? 'Unlike this artwork' : 'Like this artwork'}
    >
      {/* Heart Icon */}
      <svg
        className={`
          ${currentSize.icon}
          transition-colors duration-200
          ${isLiked 
            ? 'text-red-500 fill-current' 
            : 'text-gray-600 hover:text-red-500'
          }
          ${isLoading ? 'animate-pulse' : ''}
        `}
        fill={isLiked ? 'currentColor' : 'none'}
        stroke="currentColor"
        viewBox="0 0 24 24"
        strokeWidth={isLiked ? 0 : 2}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
        />
      </svg>

      {/* Like Count */}
      <span className={`${currentSize.text} font-medium text-gray-700`}>
        {likeCount}
      </span>

      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-3 h-3 border border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
        </div>
      )}
    </button>
  );
}