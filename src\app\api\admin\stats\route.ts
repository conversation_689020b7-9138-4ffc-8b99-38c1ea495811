import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { sql } from '@vercel/postgres';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get artwork statistics
    const artworkStats = await sql`
      SELECT 
        COUNT(*) as total_artworks,
        COUNT(CASE WHEN status = 'available' THEN 1 END) as available_artworks,
        COUNT(CASE WHEN status = 'sold' THEN 1 END) as sold_artworks,
        COALESCE(SUM(like_count), 0) as total_likes
      FROM artworks
    `;

    const stats = artworkStats.rows[0];

    return NextResponse.json({
      success: true,
      data: {
        totalArtworks: parseInt(stats.total_artworks),
        availableArtworks: parseInt(stats.available_artworks),
        soldArtworks: parseInt(stats.sold_artworks),
        totalLikes: parseInt(stats.total_likes),
      }
    });
  } catch (error) {
    console.error('Failed to fetch admin stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
}