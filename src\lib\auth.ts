import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
// import { AdminDB } from './db'; // Will be used later for session management

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials: any) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        // Check against environment variables for admin credentials
        const adminUsername = process.env.ADMIN_USERNAME;
        const adminPassword = process.env.ADMIN_PASSWORD;

        if (!adminUsername || !adminPassword) {
          console.error('Admin credentials not configured');
          return null;
        }

        // Verify username
        if (credentials.username !== adminUsername) {
          return null;
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(credentials.password, adminPassword);
        if (!isValidPassword) {
          return null;
        }

        // Return user object
        return {
          id: 'admin',
          name: 'Admin',
          email: '<EMAIL>',
        };
      },
    }),
  ],
  session: {
    strategy: 'jwt' as const,
    maxAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  callbacks: {
    async jwt({ token, user }: any) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }: any) {
      if (token && session.user) {
        session.user.id = token.id;
      }
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Utility function to hash password (for initial setup)
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// Utility function to verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate secure session token
export function generateSessionToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}