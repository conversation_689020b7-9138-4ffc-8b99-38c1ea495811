import { sql } from '@vercel/postgres';
import { Artwork, AdminSession } from '@/types';

// Database connection utility
export const db = sql;

// Artwork database operations
export class ArtworkDB {
  // Get all artworks with optional filtering (optimized for Neon free tier)
  static async getAll(filters: {
    status?: 'available' | 'sold' | 'all';
    featured?: boolean;
    limit?: number;
    offset?: number;
  } = {}) {
    const { status, featured, limit = 50, offset = 0 } = filters;
    
    // Use indexed queries for better performance
    if (featured === true && status && status !== 'all') {
      // Featured + status filter
      const result = await sql`
        SELECT id, title, description, image_url, cloudinary_public_id, 
               status, like_count, created_at, updated_at, featured, sort_order
        FROM artworks 
        WHERE featured = true AND status = ${status}
        ORDER BY sort_order ASC, created_at DESC 
        LIMIT ${limit} OFFSET ${offset}
      `;
      return result.rows as Artwork[];
    }
    
    if (featured === true) {
      // Use featured index only
      const result = await sql`
        SELECT id, title, description, image_url, cloudinary_public_id, 
               status, like_count, created_at, updated_at, featured, sort_order
        FROM artworks 
        WHERE featured = true
        ORDER BY sort_order ASC, created_at DESC 
        LIMIT ${limit} OFFSET ${offset}
      `;
      return result.rows as Artwork[];
    }
    
    if (status && status !== 'all') {
      // Use status index
      const result = await sql`
        SELECT id, title, description, image_url, cloudinary_public_id, 
               status, like_count, created_at, updated_at, featured, sort_order
        FROM artworks 
        WHERE status = ${status}
        ORDER BY created_at DESC 
        LIMIT ${limit} OFFSET ${offset}
      `;
      return result.rows as Artwork[];
    }
    
    // Default query with created_at index
    const result = await sql`
      SELECT id, title, description, image_url, cloudinary_public_id, 
             status, like_count, created_at, updated_at, featured, sort_order
      FROM artworks 
      ORDER BY created_at DESC 
      LIMIT ${limit} OFFSET ${offset}
    `;
    return result.rows as Artwork[];
  }
  
  // Get single artwork by ID
  static async getById(id: number) {
    const result = await sql`
      SELECT 
        id, title, description, image_url, cloudinary_public_id, 
        status, like_count, created_at, updated_at, featured, sort_order
      FROM artworks 
      WHERE id = ${id}
    `;
    return result.rows[0] as Artwork | undefined;
  }
  
  // Create new artwork
  static async create(data: {
    title: string;
    description?: string;
    image_url: string;
    cloudinary_public_id: string;
    status: 'available' | 'sold';
    featured?: boolean;
  }) {
    const result = await sql`
      INSERT INTO artworks (
        title, description, image_url, cloudinary_public_id, 
        status, featured, created_at, updated_at
      ) VALUES (
        ${data.title}, ${data.description || null}, ${data.image_url}, 
        ${data.cloudinary_public_id}, ${data.status}, ${data.featured || false},
        NOW(), NOW()
      ) RETURNING *
    `;
    return result.rows[0] as Artwork;
  }
  
  // Update artwork
  static async update(id: number, data: Partial<{
    title: string;
    description: string;
    image_url: string;
    cloudinary_public_id: string;
    status: 'available' | 'sold';
    featured: boolean;
  }>) {
    const updates: string[] = [];
    const values: (string | number | boolean | null)[] = [];
    let paramIndex = 1;
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        updates.push(`${key} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });
    
    if (updates.length === 0) {
      throw new Error('No fields to update');
    }
    
    updates.push(`updated_at = NOW()`);
    values.push(id);
    
    const query = `
      UPDATE artworks 
      SET ${updates.join(', ')} 
      WHERE id = $${paramIndex} 
      RETURNING *
    `;
    
    const result = await sql.query(query, values);
    return result.rows[0] as Artwork;
  }
  
  // Delete artwork
  static async delete(id: number) {
    const result = await sql`
      DELETE FROM artworks 
      WHERE id = ${id} 
      RETURNING *
    `;
    return result.rows[0] as Artwork | undefined;
  }
  
  // Get featured artworks for homepage
  static async getFeatured(limit: number = 8) {
    const result = await sql`
      SELECT 
        id, title, description, image_url, cloudinary_public_id, 
        status, like_count, created_at, updated_at, featured, sort_order
      FROM artworks 
      WHERE featured = true 
      ORDER BY sort_order ASC, created_at DESC 
      LIMIT ${limit}
    `;
    return result.rows as Artwork[];
  }
}

// Like database operations
export class LikeDB {
  // Check if user has already liked an artwork
  static async hasUserLiked(artworkId: number, userIp: string) {
    const result = await sql`
      SELECT id FROM artwork_likes 
      WHERE artwork_id = ${artworkId} AND user_ip = ${userIp}
    `;
    return result.rows.length > 0;
  }
  
  // Add a like
  static async addLike(artworkId: number, userIp: string, userAgent?: string) {
    // Start transaction
    await sql`BEGIN`;
    
    try {
      // Check if already liked
      const hasLiked = await this.hasUserLiked(artworkId, userIp);
      if (hasLiked) {
        await sql`ROLLBACK`;
        return { success: false, message: 'Already liked' };
      }
      
      // Add like record
      await sql`
        INSERT INTO artwork_likes (artwork_id, user_ip, user_agent, created_at)
        VALUES (${artworkId}, ${userIp}, ${userAgent || null}, NOW())
      `;
      
      // Update like count
      const result = await sql`
        UPDATE artworks 
        SET like_count = like_count + 1, updated_at = NOW()
        WHERE id = ${artworkId}
        RETURNING like_count
      `;
      
      await sql`COMMIT`;
      return { success: true, likeCount: result.rows[0].like_count };
    } catch (error) {
      await sql`ROLLBACK`;
      throw error;
    }
  }
  
  // Remove a like
  static async removeLike(artworkId: number, userIp: string) {
    // Start transaction
    await sql`BEGIN`;
    
    try {
      // Remove like record
      const deleteResult = await sql`
        DELETE FROM artwork_likes 
        WHERE artwork_id = ${artworkId} AND user_ip = ${userIp}
        RETURNING id
      `;
      
      if (deleteResult.rows.length === 0) {
        await sql`ROLLBACK`;
        return { success: false, message: 'Like not found' };
      }
      
      // Update like count
      const result = await sql`
        UPDATE artworks 
        SET like_count = GREATEST(like_count - 1, 0), updated_at = NOW()
        WHERE id = ${artworkId}
        RETURNING like_count
      `;
      
      await sql`COMMIT`;
      return { success: true, likeCount: result.rows[0].like_count };
    } catch (error) {
      await sql`ROLLBACK`;
      throw error;
    }
  }
  
  // Get like count for artwork
  static async getLikeCount(artworkId: number) {
    const result = await sql`
      SELECT like_count FROM artworks WHERE id = ${artworkId}
    `;
    return result.rows[0]?.like_count || 0;
  }
}

// Admin session operations
export class AdminDB {
  // Create admin session
  static async createSession(userId: string, sessionToken: string, expiresAt: Date) {
    const result = await sql`
      INSERT INTO admin_sessions (user_id, session_token, expires_at, created_at)
      VALUES (${userId}, ${sessionToken}, ${expiresAt.toISOString()}, NOW())
      RETURNING *
    `;
    return result.rows[0] as AdminSession;
  }
  
  // Get session by token
  static async getSession(sessionToken: string) {
    const result = await sql`
      SELECT * FROM admin_sessions 
      WHERE session_token = ${sessionToken} AND expires_at > NOW()
    `;
    return result.rows[0] as AdminSession | undefined;
  }
  
  // Delete session
  static async deleteSession(sessionToken: string) {
    await sql`
      DELETE FROM admin_sessions 
      WHERE session_token = ${sessionToken}
    `;
  }
  
  // Clean expired sessions (optimized for Neon free tier)
  static async cleanExpiredSessions() {
    try {
      const result = await sql`
        DELETE FROM admin_sessions 
        WHERE expires_at <= NOW()
        RETURNING id
      `;
      console.log(`Cleaned up ${result.rows.length} expired sessions`);
      return result.rows.length;
    } catch (error) {
      console.error('Session cleanup failed:', error);
      return 0;
    }
  }
}