import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Upload image to Cloudinary
export async function uploadImage(
  file: File | Buffer | string,
  options: {
    folder?: string;
    public_id?: string;
    transformation?: Record<string, unknown>[];
    quality?: string | number;
  } = {}
) {
  try {
    const {
      folder = 'painter-portfolio',
      public_id,
      transformation = [],
      quality = 'auto',
    } = options;

    // Default transformations for artwork images
    const defaultTransformations = [
      { quality },
      { fetch_format: 'auto' }, // Automatically choose best format (WebP, AVIF, etc.)
      { flags: 'progressive' }, // Progressive JPEG loading
    ];

    const uploadOptions = {
      folder,
      public_id,
      transformation: [...defaultTransformations, ...transformation],
      resource_type: 'image' as const,
      overwrite: false,
      unique_filename: !public_id, // Only use unique filename if no public_id provided
    };

    let uploadResult;

    if (file instanceof File) {
      // Convert File to buffer for upload
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      uploadResult = await cloudinary.uploader.upload(
        `data:${file.type};base64,${buffer.toString('base64')}`,
        uploadOptions
      );
    } else if (typeof file === 'string') {
      // Handle string (URL/base64)
      uploadResult = await cloudinary.uploader.upload(file, uploadOptions);
    } else {
      // Handle Buffer
      uploadResult = await cloudinary.uploader.upload(
        `data:image/jpeg;base64,${file.toString('base64')}`,
        uploadOptions
      );
    }

    return {
      success: true,
      data: {
        public_id: uploadResult.public_id,
        secure_url: uploadResult.secure_url,
        width: uploadResult.width,
        height: uploadResult.height,
        format: uploadResult.format,
        bytes: uploadResult.bytes,
      },
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

// Delete image from Cloudinary
export async function deleteImage(publicId: string) {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return {
      success: result.result === 'ok',
      data: result,
    };
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed',
    };
  }
}

// Generate optimized image URL
export function getOptimizedImageUrl(
  publicId: string,
  options: {
    width?: number;
    height?: number;
    crop?: string;
    quality?: string | number;
    format?: string;
  } = {}
) {
  const {
    width,
    height,
    crop = 'fill',
    quality = 'auto',
    format = 'auto',
  } = options;

  return cloudinary.url(publicId, {
    width,
    height,
    crop,
    quality,
    fetch_format: format,
    flags: 'progressive',
    secure: true,
  });
}

// Generate responsive image URLs for different screen sizes
export function getResponsiveImageUrls(publicId: string) {
  const breakpoints = [
    { name: 'mobile', width: 400 },
    { name: 'tablet', width: 768 },
    { name: 'desktop', width: 1200 },
    { name: 'large', width: 1600 },
  ];

  return breakpoints.reduce((urls, { name, width }) => {
    urls[name] = getOptimizedImageUrl(publicId, { width });
    return urls;
  }, {} as Record<string, string>);
}

// Generate blur placeholder for loading states
export function getBlurPlaceholder(publicId: string) {
  return cloudinary.url(publicId, {
    width: 40,
    height: 30,
    crop: 'fill',
    quality: 10,
    fetch_format: 'auto',
    flags: 'progressive',
    secure: true,
  });
}

// Validate image file
export function validateImageFile(file: File) {
  const maxSize = 10 * 1024 * 1024; // 10MB - within Cloudinary free tier limits
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload JPEG, PNG, WebP, or GIF images.',
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File too large. Please upload images smaller than 10MB.',
    };
  }

  return { valid: true };
}

// Get Cloudinary usage stats (for monitoring free tier limits)
export async function getCloudinaryUsage() {
  try {
    const usage = await cloudinary.api.usage();
    return {
      success: true,
      data: {
        storage: {
          used: usage.storage.used_bytes,
          limit: usage.storage.limit,
          percentage: (usage.storage.used_bytes / usage.storage.limit) * 100,
        },
        bandwidth: {
          used: usage.bandwidth.used_bytes,
          limit: usage.bandwidth.limit,
          percentage: (usage.bandwidth.used_bytes / usage.bandwidth.limit) * 100,
        },
        requests: {
          used: usage.requests,
          limit: usage.requests_limit,
          percentage: (usage.requests / usage.requests_limit) * 100,
        },
      },
    };
  } catch (error) {
    console.error('Failed to get Cloudinary usage:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get usage',
    };
  }
}