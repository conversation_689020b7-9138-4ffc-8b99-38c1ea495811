import { MetadataRoute } from 'next';
import { ArtworkDB } from '@/lib/db';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXTAUTH_URL || 'https://artist-sooty.vercel.app';
  
  // Static routes
  const staticRoutes = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/gallery`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
  ];
  
  // Try to get artworks for dynamic routes, but handle build-time errors gracefully
  let artworkRoutes: MetadataRoute.Sitemap = [];
  try {
    const artworks = await ArtworkDB.getAll({ limit: 1000 });
    artworkRoutes = artworks.map((artwork) => ({
      url: `${baseUrl}/gallery#artwork-${artwork.id}`,
      lastModified: new Date(artwork.updated_at),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    }));
  } catch (error) {
    // During build time, database might not be available
    console.log('Database not available during sitemap generation, using static routes only');
  }
  
  return [...staticRoutes, ...artworkRoutes];
}