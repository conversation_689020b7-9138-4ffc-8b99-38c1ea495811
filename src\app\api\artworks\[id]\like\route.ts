import { NextRequest, NextResponse } from 'next/server';
import { LikeDB } from '@/lib/db';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const artworkId = parseInt(resolvedParams.id);
    
    if (isNaN(artworkId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid artwork ID' },
        { status: 400 }
      );
    }

    // Get user IP address
    const forwarded = request.headers.get('x-forwarded-for');
    const userIp = forwarded ? forwarded.split(',')[0] : 
                   request.headers.get('x-real-ip') || 
                   'unknown';

    // Get user agent
    const userAgent = request.headers.get('user-agent') || undefined;

    // Check if user has already liked this artwork
    const hasLiked = await LikeDB.hasUserLiked(artworkId, userIp);
    
    if (hasLiked) {
      // Remove like (toggle functionality)
      const result = await LikeDB.removeLike(artworkId, userIp);
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: {
            likeCount: result.likeCount,
            isLiked: false,
            action: 'unliked'
          }
        });
      } else {
        return NextResponse.json(
          { success: false, error: result.message },
          { status: 400 }
        );
      }
    } else {
      // Add like
      const result = await LikeDB.addLike(artworkId, userIp, userAgent);
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: {
            likeCount: result.likeCount,
            isLiked: true,
            action: 'liked'
          }
        });
      } else {
        return NextResponse.json(
          { success: false, error: result.message },
          { status: 400 }
        );
      }
    }
  } catch (error) {
    console.error('Like operation failed:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process like' },
      { status: 500 }
    );
  }
}

// GET endpoint to check if user has liked an artwork
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const artworkId = parseInt(resolvedParams.id);
    
    if (isNaN(artworkId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid artwork ID' },
        { status: 400 }
      );
    }

    // Get user IP address
    const forwarded = request.headers.get('x-forwarded-for');
    const userIp = forwarded ? forwarded.split(',')[0] : 
                   request.headers.get('x-real-ip') || 
                   'unknown';

    // Check if user has liked this artwork
    const hasLiked = await LikeDB.hasUserLiked(artworkId, userIp);
    const likeCount = await LikeDB.getLikeCount(artworkId);

    return NextResponse.json({
      success: true,
      data: {
        isLiked: hasLiked,
        likeCount: likeCount
      }
    });
  } catch (error) {
    console.error('Failed to check like status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check like status' },
      { status: 500 }
    );
  }
}