import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { ArtworkDB } from '@/lib/db';
import { artworkValidation } from '@/lib/validation';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate artwork data
    const validation = artworkValidation.validateCreate(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Sanitize data
    const sanitizedData = artworkValidation.sanitizeCreate(body);

    // Create artwork
    const artwork = await ArtworkDB.create(sanitizedData);

    return NextResponse.json({
      success: true,
      data: artwork
    });
  } catch (error) {
    console.error('Failed to create artwork:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create artwork' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all artworks for admin
    const artworks = await ArtworkDB.getAll({ limit: 100 });

    return NextResponse.json({
      success: true,
      data: artworks
    });
  } catch (error) {
    console.error('Failed to fetch artworks:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch artworks' },
      { status: 500 }
    );
  }
}